const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const db = require('./db');
const session = require('express-session');
const {
  generalLimiter,
  strictLimiter,
  adminLimiter,
  ratingLimiter,
  testLimiter
} = require('./middleware/rateLimiter');
const {
  handleValidationErrors,
  validateId,
  validateProductId,
  validateAdmin,
  validateBrand,
  validateCategory,
  validateProduct,
  validateVariant,
  validatePhoto,
  validateRating,
  validateBanner
} = require('./middleware/validation');

// Import CRUD modules
const adminCRUD = require('./database/adminCRUD');
const brandCRUD = require('./database/brandCRUD');
const categoryCRUD = require('./database/categoryCRUD');
const productCRUD = require('./database/productCRUD');
const variantCRUD = require('./database/variantCRUD');
const photoCRUD = require('./database/photoCRUD');
const ratingCRUD = require('./database/ratingCRUD');
const bannerCRUD = require('./database/bannerCRUD');

require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Security Middleware
app.use(helmet()); // Set security headers
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true
}));
app.use(generalLimiter); // Apply general rate limiting to all requests
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: true,
  cookie: {
    maxAge: 1000 * 60 * 60 * 24 // 24 hours
    //secure: true; use if using https
  }
}));

// Body parsing middleware with size limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Test database connection
app.get('/api/test-db', testLimiter, async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT 1 as test');
    res.json({
      success: true,
      message: 'Database connection successful',
      data: rows
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Database connection failed',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'GG Catalog API'
  });
});

// ===== ADMIN ROUTES =====

// Get all admins
app.get('/api/admins', adminLimiter, adminCRUD.getAllAdmins);

// Create new admin
app.post('/api/admins', adminLimiter, validateAdmin, handleValidationErrors, adminCRUD.createAdmin);

app.

// ===== BRAND ROUTES =====

// Get all brands
app.get('/api/brands', brandCRUD.getAllBrands);

// Get brand by ID
app.get('/api/brands/:id', validateId, handleValidationErrors, brandCRUD.getBrandById);

// Create new brand
app.post('/api/brands', strictLimiter, validateBrand, handleValidationErrors, brandCRUD.createBrand);

// Update brand
app.put('/api/brands/:id', strictLimiter, validateId, validateBrand, handleValidationErrors, brandCRUD.updateBrand);

// Delete brand
app.delete('/api/brands/:id', strictLimiter, validateId, handleValidationErrors, brandCRUD.deleteBrand);

// ===== CATEGORY ROUTES =====

// Get all categories
app.get('/api/categories', categoryCRUD.getAllCategories);

// Get category by ID
app.get('/api/categories/:id', validateId, handleValidationErrors, categoryCRUD.getCategoryById);

// Create new category
app.post('/api/categories', strictLimiter, validateCategory, handleValidationErrors, categoryCRUD.createCategory);

// Update category
app.put('/api/categories/:id', strictLimiter, validateId, validateCategory, handleValidationErrors, categoryCRUD.updateCategory);

// Delete category
app.delete('/api/categories/:id', strictLimiter, validateId, handleValidationErrors, categoryCRUD.deleteCategory);

// ===== PRODUCT ROUTES =====

// Get all products with brand and category info
app.get('/api/products', productCRUD.getAllProducts);

// Get product by ID with variants and photos
app.get('/api/products/:id', productCRUD.getProductById);

// Create new product
app.post('/api/products', productCRUD.createProduct);

// Update product
app.put('/api/products/:id', productCRUD.updateProduct);

// Delete product
app.delete('/api/products/:id', productCRUD.deleteProduct);

// ===== PRODUCT VARIANT ROUTES =====

// Get variants for a product
app.get('/api/products/:productId/variants', variantCRUD.getVariantsByProductId);

// Add variant to product
app.post('/api/products/:productId/variants', variantCRUD.createVariant);

// Update variant
app.put('/api/variants/:id', variantCRUD.updateVariant);

// Delete variant
app.delete('/api/variants/:id', variantCRUD.deleteVariant);

// ===== PRODUCT PHOTO ROUTES =====

// Get photos for a product
app.get('/api/products/:productId/photos', photoCRUD.getPhotosByProductId);

// Add photo to product
app.post('/api/products/:productId/photos', photoCRUD.createPhoto);

// Delete photo
app.delete('/api/photos/:id', photoCRUD.deletePhoto);

// ===== RATING ROUTES =====

// Get ratings for a product
app.get('/api/products/:productId/ratings', ratingCRUD.getRatingsByProductId);

// Add rating to product
app.post('/api/products/:productId/ratings', ratingLimiter, validateProductId, validateRating, handleValidationErrors, ratingCRUD.createRating);

// ===== WEB BANNER ROUTES =====

// Get all web banners
app.get('/api/banners', bannerCRUD.getAllBanners);

// Get active web banners only
app.get('/api/banners/active', bannerCRUD.getActiveBanners);

// Create new banner
app.post('/api/banners', strictLimiter, validateBanner, handleValidationErrors, bannerCRUD.createBanner);

// Update banner
app.put('/api/banners/:id', strictLimiter, validateId, validateBanner, handleValidationErrors, bannerCRUD.updateBanner);

// Delete banner
app.delete('/api/banners/:id', strictLimiter, validateId, handleValidationErrors, bannerCRUD.deleteBanner);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 GG Catalog API Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔍 Database test: http://localhost:${PORT}/api/test-db`);
});

module.exports = app;
