const db = require('../db');
const bcrypt = require('bcrypt');

const adminLogin = async (req, res, next) => {    
  try {
    const { username, password } = req.body;

    // Check if username and password match
    const [existingAdmin] = await db.execute('SELECT * FROM admins WHERE username = ? AND password_hash = ?', [username, password]);
    if (existingAdmin.length === 0) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Set session
    req.session.admin = existingAdmin[0];
    res.json({ success: true, message: 'Admin logged in successfully' });

    // set timeout to 24 hours
    req.session.cookie.maxAge = 1000 * 60 * 60 * 24;
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

const admninLogout = async (req, res, next) => {    
  try {
    req.session.destroy();
    res.json({ success: true, message: 'Admin logged out successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
    adminLogin,
    admninLogout
};